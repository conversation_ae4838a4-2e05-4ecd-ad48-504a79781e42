/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #0a0a0a;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    width: 100%;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #000000 !important;
    background-color: #000000 !important;
    padding: 12px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    min-height: 60px;
}

/* Ensure header stays black when scrolling */
.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000000;
    z-index: -1;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: transparent;
    width: 100%;
    box-sizing: border-box;
    overflow: visible;
    height: 100%;
    min-height: 60px;
}

/* Tablet and Large Mobile */
@media (max-width: 768px) {
    .header {
        padding: 8px 0;
        min-height: 55px;
        width: 100%;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
    }

    .nav-container {
        padding: 0 12px;
        max-width: 100%;
        width: 100%;
        overflow: visible;
        position: relative;
        min-height: 55px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .logo {
        flex-shrink: 0;
        z-index: 1001;
        max-width: 40%;
    }

    .logo-img {
        height: 30px;
        flex-shrink: 0;
        filter: brightness(1.2) contrast(1.1);
        max-width: 100%;
    }

    .nav-buttons {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
        align-items: center;
        z-index: 1001;
        flex: 1;
        justify-content: flex-end;
    }

    .nav-buttons .btn {
        padding: 8px 14px;
        font-size: 0.85rem;
        min-width: 60px;
        font-weight: 600;
        border-radius: 16px;
        height: 34px;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        transition: all 0.3s ease;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    .nav-buttons .btn:active {
        transform: scale(0.95);
    }

    .mobile-menu-toggle {
        display: none;
    }

    .mobile-nav {
        display: none;
    }

    .mobile-nav.active {
        display: block;
    }
}

/* Medium Mobile */
@media (max-width: 480px) {
    .header {
        padding: 6px 0;
        min-height: 50px;
        width: 100%;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.35);
    }

    .nav-container {
        padding: 0 10px;
        min-height: 50px;
        width: 100%;
        max-width: 100%;
    }

    .logo {
        max-width: 35%;
    }

    .logo-img {
        height: 26px;
        filter: brightness(1.2) contrast(1.1);
    }

    .nav-buttons {
        gap: 6px;
        flex: 1;
        justify-content: flex-end;
    }

    .nav-buttons .btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        min-width: 50px;
        height: 30px;
        border-radius: 14px;
        font-weight: 600;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    .mobile-menu-toggle {
        padding: 8px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }

    .mobile-menu-toggle:hover {
        background: rgba(255, 107, 53, 0.2);
    }

    .mobile-nav-buttons .btn {
        width: 220px;
        padding: 16px 28px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 25px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
}

.logo {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.logo-img {
    height: 45px;
    width: auto;
    filter: brightness(1.1) contrast(1.1);
    display: block;
}

.nav-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-shrink: 0;
}

.btn {
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    font-size: 0.9rem;
    font-size: 14px;
    border: 2px solid transparent;
}

.btn-login {
    background: #ffffff;
    color: #000000;
    border: 2px solid #ffffff;
    font-size: inherit;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(255, 255, 255, 0.2);
}

.btn-login:hover {
    background: #f0f0f0;
    color: #000000;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.btn-register {
    background: #ffc107;
    color: #000000;
    border: 2px solid #ffc107;
    font-size: inherit;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.2);
}

.btn-register:hover {
    background: #e0a800;
    border-color: #e0a800;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 5px;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 107, 53, 0.1);
}

.mobile-menu-toggle span {
    width: 28px;
    height: 3px;
    background: #ffffff;
    border-radius: 2px;
    transition: all 0.3s ease;
    display: block;
}

.mobile-menu-toggle:hover span {
    background: #ff6b35;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
    background: #ff6b35;
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
    background: #ff6b35;
}

.mobile-nav {
    display: none;
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    padding: 35px 25px;
    z-index: 999;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.6);
    border-bottom: 4px solid #ff6b35;
    animation: slideDown 0.4s ease-out;
    min-height: 220px;
    backdrop-filter: blur(10px);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-nav-content {
    text-align: center;
    max-width: 300px;
    margin: 0 auto;
}

.mobile-logo img {
    height: 55px;
    margin-bottom: 30px;
    filter: brightness(1.2) contrast(1.1);
    transition: transform 0.3s ease;
}

.mobile-logo img:hover {
    transform: scale(1.05);
}

.mobile-nav-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    margin-top: 20px;
}

.mobile-nav-buttons .btn {
    width: 240px;
    padding: 18px 35px;
    font-size: 1.2rem;
    font-weight: 700;
    text-align: center;
    border-radius: 25px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.mobile-nav-buttons .btn-login {
    background: #fff;
    color: #ff6b35;
    border: 2px solid #ff6b35;
}

.mobile-nav-buttons .btn-login:hover {
    background: #ff6b35;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.mobile-nav-buttons .btn-register {
    background: #dc3545;
    color: #fff;
    border: 2px solid #dc3545;
}

.mobile-nav-buttons .btn-register:hover {
    background: #c82333;
    border-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Main Content */
.main {
    margin-top: 70px;
    background: #0a0a0a;
}

/* Responsive main content margins */
@media (max-width: 768px) {
    .main {
        margin-top: 65px;
    }
}

@media (max-width: 480px) {
    .main {
        margin-top: 60px;
    }
}

@media (max-width: 400px) {
    .main {
        margin-top: 55px;
    }
}

@media (max-width: 320px) {
    .main {
        margin-top: 50px;
    }
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    padding: 80px 0;
    text-align: center;
    color: #fff;
    position: relative;
    overflow: hidden;
    margin: 0;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 107, 53, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 40px;
    line-height: 1.2;
    position: relative;
    z-index: 2;
}

.hero-image {
    text-align: center;
    margin: 30px 0;
    position: relative;
    z-index: 2;
}

.hero-image a {
    display: inline-block;
    text-decoration: none;
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.hero-image a:hover {
    transform: scale(1.02);
}

.hero-main-img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    display: block;
}

.hero-main-img:hover {
    transform: scale(1.02);
}

.brand-ph {
    color: #ff6b35;
}

.brand-lago {
    color: #f7931e;
}

.welcome-section {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 15px;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-text p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    line-height: 1.7;
}

/* Promotional Banner Section */
.promo-banner {
    padding: 20px 0;
    background: #111;
    position: relative;
    overflow: hidden;
}

.banner-slider {
    position: relative;
    width: 100%;
    height: 420px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
    background: #1a1a1a;
    margin: 0 auto;
    max-width: 1100px;
    touch-action: pan-y;
}

.banner-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a1a1a;
    padding: 0;
    box-sizing: border-box;
}

.banner-slide.active {
    opacity: 1;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
    transition: transform 0.3s ease;
    display: block;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.banner-slide img:hover {
    transform: scale(1.02);
}

.banner-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #ff8c00;
    transform: scale(1.2);
}

.banner-arrows {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.arrow-left, .arrow-right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.8rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.arrow-left {
    left: 10px;
}

.arrow-right {
    right: 10px;
}

.arrow-left:hover, .arrow-right:hover {
    background: rgba(255, 140, 0, 0.8);
    border-color: #fff;
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.4);
}

/* Invite Section */
.invite-section {
    padding: 40px 0;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.invite-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.invite-btn {
    display: inline-block;
    background: #fff;
    color: #ff6b35;
    padding: 15px 40px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
    min-height: 60px;
    width: auto;
    min-width: 250px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0;
    white-space: nowrap;
}

.invite-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    background: #f8f8f8;
}

.invite-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Floating Play Now Button */
.floating-play-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: #fff;
    padding: 15px 25px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    opacity: 0;
    transform: translateY(100px);
    pointer-events: none;
    min-height: 50px;
    min-width: 50px;
    justify-content: center;
}

.floating-play-btn.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.floating-play-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 107, 53, 0.6);
}

.floating-play-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.floating-play-btn .play-icon {
    font-size: 1.2rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Content Sections */
.about, .slots, .payouts, .support, .signup, .bonuses, .security, .responsible, .tips, .faq, .conclusion {
    padding: 60px 0;
    background: #111;
    color: #fff;
    position: relative;
}

.about:nth-child(odd), .slots:nth-child(odd), .payouts:nth-child(odd), .support:nth-child(odd), .signup:nth-child(odd), .bonuses:nth-child(odd), .security:nth-child(odd), .responsible:nth-child(odd), .tips:nth-child(odd), .faq:nth-child(odd), .conclusion:nth-child(odd) {
    background: #0a0a0a;
}

.about h2, .slots h2, .payouts h2, .support h2, .signup h2, .bonuses h2, .security h2, .responsible h2, .tips h2, .faq h2, .conclusion h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.about p, .slots p, .payouts p, .support p, .security p, .responsible p, .conclusion p {
    font-size: 1.1rem;
    line-height: 1.8;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Steps and Lists */
.steps, .bonus-list, .tips-list, .faq-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.step, .bonus-item, .tip-item, .faq-item {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 107, 53, 0.1);
}

.step:hover, .bonus-item:hover, .tip-item:hover, .faq-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 107, 53, 0.3);
}

.step h3, .bonus-item h3, .tip-item h3, .faq-item h3 {
    color: #ff6b35;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.step p, .bonus-item p, .tip-item p, .faq-item p {
    font-size: 1rem;
    line-height: 1.6;
    text-align: left;
    opacity: 0.9;
}

/* Telegram and Recharge Images */
.telegram, .recharge, .rebates {
    padding: 40px 0;
    background: #111;
    text-align: center;
}

.telegram-images, .recharge-images, .rebates-images {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.telegram-images img, .recharge-images img, .rebates-images img, .rebates-img {
    max-width: 400px;
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
}

.telegram-images img:hover, .recharge-images img:hover, .rebates-images img:hover, .rebates-img:hover {
    transform: scale(1.05);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: #fff;
    padding: 60px 0 30px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 107, 53, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.footer-content {
    text-align: center;
}

.footer-logo img {
    height: 60px;
    margin-bottom: 40px;
    filter: brightness(1.1) contrast(1.1);
    transition: transform 0.3s ease;
}

.footer-logo img:hover {
    transform: scale(1.05);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 50px;
}

.footer-section h3 {
    color: #ff6b35;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 12px;
}

.footer-section ul li a {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 5px;
    display: inline-block;
}

.footer-section ul li a:hover {
    color: #f7931e;
    background: rgba(255, 107, 53, 0.1);
    transform: translateX(5px);
}

.provider-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-bottom: 40px;
    padding: 20px 0;
}

.provider-logos img {
    height: 50px;
    width: auto;
    transition: all 0.3s ease;
    filter: brightness(0.9) contrast(1.1);
    border-radius: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
}

.provider-logos img:hover {
    transform: scale(1.1);
    filter: brightness(1.1) contrast(1.2);
    background: rgba(255, 107, 53, 0.1);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
}

.footer-copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 30px;
    text-align: center;
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Responsive Design - Additional mobile styles */
@media (max-width: 768px) {
    .mobile-nav-buttons .btn {
        width: 180px;
        padding: 12px 20px;
        font-size: 1rem;
        font-weight: 600;
        margin: 5px 0;
    }

    .hero {
        padding: 40px 0 30px;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        margin: 0;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.3;
        margin-bottom: 25px;
        padding: 0 15px;
        text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
        font-weight: 800;
    }

    .hero-image {
        margin: 20px 0;
        padding: 0 15px;
    }

    .hero-image a {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
        border-radius: 12px;
    }

    .hero-main-img {
        border-radius: 12px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        width: 100%;
        max-width: 100%;
    }

    .welcome-title {
        font-size: 1.7rem;
        line-height: 1.4;
        margin-bottom: 20px;
        padding: 0 15px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .welcome-text {
        padding: 0 15px;
        font-size: 1.05rem;
        line-height: 1.7;
    }

    .welcome-text p {
        margin-bottom: 18px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .gallery-track img {
        height: 150px;
        width: 200px;
    }

    /* Content sections mobile optimization */
    .container {
        padding: 0 15px;
    }

    .about, .slots, .payouts, .support, .signup, .bonuses, .security, .responsible, .tips, .faq, .conclusion {
        padding: 50px 0;
    }

    .about h2, .slots h2, .payouts h2, .support h2, .signup h2, .bonuses h2, .security h2, .responsible h2, .tips h2, .faq h2, .conclusion h2 {
        font-size: 2rem;
        line-height: 1.3;
        margin-bottom: 25px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        padding: 0 10px;
    }

    .about p, .slots p, .payouts p, .support p, .signup p, .bonuses p, .security p, .responsible p, .tips p, .faq p, .conclusion p {
        font-size: 1.05rem;
        line-height: 1.7;
        margin-bottom: 18px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        padding: 0 10px;
    }

    .steps, .bonus-list, .tips-list, .faq-list {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-top: 30px;
    }

    .step, .bonus-item, .tip-item, .faq-item {
        padding: 25px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    }

    .step h3, .bonus-item h3, .tip-item h3, .faq-item h3 {
        font-size: 1.2rem;
        margin-bottom: 12px;
    }

    .step p, .bonus-item p, .tip-item p, .faq-item p {
        font-size: 0.95rem;
        line-height: 1.6;
    }
    
    .telegram-images, .recharge-images {
        flex-direction: column;
        align-items: center;
    }
    
    .footer {
        padding: 50px 0 25px;
    }

    .footer-logo img {
        height: 50px;
        margin-bottom: 30px;
    }

    .footer-links {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
    }

    .footer-section h3 {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }

    .footer-section ul li {
        margin-bottom: 10px;
    }

    .footer-section ul li a {
        font-size: 0.95rem;
        padding: 8px 12px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
    }

    .provider-logos {
        gap: 20px;
        padding: 15px 0;
    }

    .provider-logos img {
        height: 45px;
        padding: 10px;
        border-radius: 10px;
    }

    .footer-copyright {
        padding-top: 25px;
        font-size: 0.85rem;
    }

    /* Banner responsive styles */
    .promo-banner {
        padding: 15px 0;
        background: #0f0f0f;
    }

    .banner-slider {
        height: 280px;
        border-radius: 12px;
        max-width: 95%;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
        touch-action: pan-y;
    }

    .banner-slide {
        padding: 0;
        border-radius: 12px;
    }

    .banner-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border-radius: 12px;
        transition: transform 0.3s ease;
    }

    .banner-slide img:active {
        transform: scale(0.98);
    }

    .arrow-left, .arrow-right {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
        border-width: 2px;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .arrow-left {
        left: 8px;
    }

    .arrow-right {
        right: 8px;
    }

    .arrow-left:hover, .arrow-right:hover {
        background: rgba(255, 140, 0, 0.9);
        transform: translateY(-50%) scale(1.1);
    }

    .banner-dots {
        margin-top: 15px;
        gap: 10px;
    }

    .dot {
        width: 10px;
        height: 10px;
        background: rgba(255, 255, 255, 0.4);
        transition: all 0.3s ease;
    }

    .dot.active {
        background: #ff8c00;
        transform: scale(1.3);
        box-shadow: 0 0 10px rgba(255, 140, 0, 0.5);
    }

    /* Floating button responsive */
    .floating-play-btn {
        bottom: 20px;
        right: 20px;
        padding: 14px 22px;
        font-size: 1rem;
        min-height: 52px;
        min-width: 52px;
        border-radius: 26px;
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5);
    }

    .floating-play-btn:hover {
        transform: translateY(-2px) scale(1.03);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.6);
    }

    .floating-play-btn:active {
        transform: translateY(0) scale(0.96);
    }

    .floating-play-btn .play-icon {
        font-size: 1.1rem;
    }
}

/* Extra small mobile devices (phones in portrait) - Additional styles */
@media (max-width: 480px) {
    .mobile-nav-buttons .btn {
        width: 200px;
        padding: 16px 25px;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .mobile-logo img {
        height: 45px;
        margin-bottom: 20px;
    }
}

/* Small Mobile */
@media (max-width: 400px) {
    .header {
        padding: 8px 0;
        min-height: 50px;
        width: 100%;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .nav-container {
        padding: 0 10px;
        min-height: 50px;
        width: 100%;
        max-width: 100%;
    }

    .logo-img {
        height: 28px;
        filter: brightness(1.2) contrast(1.1);
    }

    .nav-buttons {
        gap: 6px;
    }

    .nav-buttons .btn {
        padding: 7px 12px;
        font-size: 0.8rem;
        min-width: 52px;
        height: 32px;
        border-radius: 12px;
        font-weight: 600;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
}

/* Very Small Mobile */
@media (max-width: 320px) {
    .header {
        padding: 4px 0;
        min-height: 42px;
        width: 100%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
    }

    .nav-container {
        padding: 0 6px;
        min-height: 42px;
        width: 100%;
        max-width: 100%;
    }

    .logo {
        max-width: 30%;
    }

    .logo-img {
        height: 22px;
        filter: brightness(1.2) contrast(1.1);
    }

    .nav-buttons {
        gap: 3px;
        flex: 1;
        justify-content: flex-end;
    }

    .nav-buttons .btn {
        padding: 4px 8px;
        font-size: 0.7rem;
        min-width: 42px;
        height: 26px;
        border-radius: 10px;
        font-weight: 600;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    }

    .mobile-nav {
        padding: 15px 10px;
        top: 45px;
    }

    .mobile-nav-buttons .btn {
        width: 180px;
        padding: 12px 20px;
        font-size: 1rem;
        font-weight: 600;
    }

    .mobile-logo img {
        height: 40px;
        margin-bottom: 15px;
    }

    .mobile-menu-toggle {
        padding: 4px;
    }

    .mobile-menu-toggle span {
        width: 20px;
        height: 2px;
    }

    .hero {
        padding: 30px 0 25px;
        margin: 0;
    }

    .hero-title {
        font-size: 1.6rem;
        line-height: 1.3;
        margin-bottom: 20px;
        padding: 0 10px;
        text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
        font-weight: 800;
    }

    .hero-image {
        margin: 15px 0;
        padding: 0 10px;
    }

    .hero-image a {
        border-radius: 10px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }

    .hero-main-img {
        border-radius: 10px;
        width: 100%;
    }

    .welcome-title {
        font-size: 1.4rem;
        line-height: 1.3;
        margin-bottom: 15px;
        padding: 0 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .welcome-text {
        padding: 0 10px;
        font-size: 1rem;
        line-height: 1.6;
    }

    .welcome-text p {
        margin-bottom: 16px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .promo-banner {
        padding: 12px 0;
        background: #0a0a0a;
    }

    .banner-slider {
        height: 200px;
        border-radius: 10px;
        max-width: 92%;
        margin: 0 auto;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.6);
        touch-action: pan-y;
    }

    .banner-slide {
        padding: 0;
        border-radius: 10px;
    }

    .banner-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border-radius: 10px;
        transition: transform 0.2s ease;
    }

    .banner-slide img:active {
        transform: scale(0.97);
    }

    .arrow-left, .arrow-right {
        width: 38px;
        height: 38px;
        font-size: 1rem;
        border-width: 1px;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(6px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .arrow-left {
        left: 6px;
    }

    .arrow-right {
        right: 6px;
    }

    .arrow-left:hover, .arrow-right:hover {
        background: rgba(255, 140, 0, 0.9);
        transform: translateY(-50%) scale(1.05);
    }

    .banner-dots {
        margin-top: 12px;
        gap: 8px;
    }

    .dot {
        width: 8px;
        height: 8px;
        background: rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .dot.active {
        background: #ff8c00;
        transform: scale(1.2);
        box-shadow: 0 0 8px rgba(255, 140, 0, 0.4);
    }

    .container {
        padding: 0 10px;
    }

    .about, .slots, .payouts, .support, .signup, .bonuses, .security, .responsible, .tips, .faq, .conclusion {
        padding: 40px 0;
    }

    .about h2, .slots h2, .payouts h2, .support h2, .signup h2, .bonuses h2, .security h2, .responsible h2, .tips h2, .faq h2, .conclusion h2 {
        font-size: 1.7rem;
        line-height: 1.3;
        margin-bottom: 20px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        padding: 0 5px;
    }

    .about p, .slots p, .payouts p, .support p, .signup p, .bonuses p, .security p, .responsible p, .tips p, .faq p, .conclusion p {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 15px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        padding: 0 5px;
    }

    .steps, .bonus-list, .tips-list, .faq-list {
        gap: 20px;
        margin-top: 25px;
    }

    .step, .bonus-item, .tip-item, .faq-item {
        padding: 20px 15px;
        border-radius: 10px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }

    .step h3, .bonus-item h3, .tip-item h3, .faq-item h3 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .step p, .bonus-item p, .tip-item p, .faq-item p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .floating-play-btn {
        width: 50px;
        height: 50px;
        bottom: 15px;
        right: 15px;
    }

    .floating-play-btn .play-icon {
        font-size: 1rem;
    }

    .banner-dots {
        margin-top: 10px;
        gap: 6px;
    }

    .dot {
        width: 6px;
        height: 6px;
    }

    /* Floating button responsive */
    .floating-play-btn {
        bottom: 10px;
        right: 10px;
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .floating-play-btn .play-icon {
        font-size: 1rem;
    }

    /* Mobile-specific improvements */
    .signup-steps, .bonuses-list, .tips-list {
        padding: 0 5px;
    }

    .signup-steps li, .bonuses-list li, .tips-list li {
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 10px;
        padding: 8px 0;
    }

    .invite-section {
        padding: 35px 0;
    }

    .invite-btn {
        padding: 15px 35px;
        font-size: 1.1rem;
        margin: 20px 0;
        min-height: 56px;
        border-radius: 28px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        font-weight: 700;
        letter-spacing: 0;
        width: auto;
        min-width: 220px;
        text-align: center;
        white-space: nowrap;
    }

    .invite-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    }

    .invite-btn:active {
        transform: translateY(0);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    }

    .footer {
        padding: 30px 0 20px;
    }

    .footer-content {
        flex-direction: column;
        gap: 25px;
    }

    .footer-section h3 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .footer-section ul li {
        margin-bottom: 8px;
    }

    .footer-section ul li a {
        font-size: 0.9rem;
    }

    .provider-logos {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .provider-logos img {
        width: 60px;
        height: 40px;
    }

    /* Touch-friendly improvements */
    .banner-dots .dot {
        min-width: 44px;
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        transition: all 0.3s ease;
        position: relative;
    }

    .banner-dots .dot::before {
        content: '';
        width: 10px;
        height: 10px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .banner-dots .dot.active::before {
        background: #ff8c00;
        transform: scale(1.3);
        box-shadow: 0 0 12px rgba(255, 140, 0, 0.6);
    }

    .banner-dots .dot:hover::before {
        background: rgba(255, 140, 0, 0.7);
        transform: scale(1.1);
    }

    /* Improved text readability */
    .hero {
        padding: 60px 0 40px;
    }

    .welcome-section {
        margin-top: 20px;
    }
}

/* Medium tablets */
@media (min-width: 769px) and (max-width: 1024px) {
    .banner-slider {
        height: 350px;
    }

    .banner-slide {
        padding: 0;
    }

    .arrow-left, .arrow-right {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .arrow-left {
        left: 15px;
    }

    .arrow-right {
        right: 15px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }
}

/* Mobile-Specific Enhancements */
@media (max-width: 768px) {
    /* Remove any unwanted spacing */
    html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        overflow-x: hidden;
    }

    /* Ensure full width sections */
    .hero, .promo-banner, .invite-section, .about, .slots, .payouts, .support, .signup, .bonuses, .security, .responsible, .tips, .faq, .conclusion, .telegram, .recharge, .rebates, .footer {
        margin: 0;
        width: 100%;
        box-sizing: border-box;
    }

    /* Improved touch targets */
    button, .btn, a[role="button"] {
        min-height: 44px;
        min-width: 44px;
    }

    /* Better text selection */
    * {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    /* Allow text selection for content */
    p, h1, h2, h3, h4, h5, h6, span, div.welcome-text {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }

    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px;
    }

    /* Improved tap highlighting */
    * {
        -webkit-tap-highlight-color: rgba(255, 107, 53, 0.2);
    }

    /* Better image loading */
    img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    /* Optimized animations for mobile */
    @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
}

/* Mobile landscape optimizations */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: 40px 0 30px;
    }

    .hero-title {
        font-size: 1.6rem;
        margin-bottom: 20px;
    }

    .welcome-title {
        font-size: 1.4rem;
        margin-bottom: 15px;
    }

    .banner-slider {
        height: 200px;
    }

    .about, .slots, .payouts, .support, .signup, .bonuses, .security, .responsible, .tips, .faq, .conclusion {
        padding: 30px 0;
    }
}
